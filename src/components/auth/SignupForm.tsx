'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import { SignupFormState, SignupFormData, AccountType } from '@/types/auth';
import { supabase } from '@/lib/supabase';
import { AccountTypeSelection } from './signup/AccountTypeSelection';
import { BasicInformation } from './signup/BasicInformation';
import { EmailVerification } from './signup/EmailVerification';
import { ProfileCompletion } from './signup/ProfileCompletion';

export function SignupForm() {
  const router = useRouter();
  const locale = useLocale();
  const searchParams = useSearchParams();
  const t = useTranslations('Auth.Signup');
  const invitationToken = searchParams.get('token');

  const [formState, setFormState] = useState<SignupFormState>({
    step: 1,
    data: {},
    loading: false,
    error: null,
    email_sent: false,
  });

  interface InvitationData {
    email: string;
    organization_name?: string;
  }

  const [invitationData, setInvitationData] = useState<InvitationData | null>(
    null,
  );

  // Load invitation data if token is present
  useEffect(() => {
    if (invitationToken) {
      loadInvitationData(invitationToken);
    }
  }, [invitationToken]);

  const loadInvitationData = async (token: string) => {
    try {
      const response = await fetch(`/api/invitations/${token}`);
      const result = await response.json();

      if (result.success) {
        setInvitationData(result.data);
        // Pre-fill form data for invitation signup
        setFormState((prev) => ({
          ...prev,
          data: {
            ...prev.data,
            email: result.data.email,
            account_type: 'company', // Invitations are always for company accounts
          },
        }));
      } else {
        setFormState((prev) => ({
          ...prev,
          error: result.error || 'Invalid invitation',
        }));
      }
    } catch (error) {
      console.error('Error loading invitation:', error);
      setFormState((prev) => ({
        ...prev,
        error: 'Failed to load invitation data',
      }));
    }
  };

  const updateFormData = (data: Partial<SignupFormData>) => {
    setFormState((prev) => ({
      ...prev,
      data: { ...prev.data, ...data },
    }));
  };

  const nextStep = () => {
    setFormState((prev) => ({
      ...prev,
      step: prev.step + 1,
      error: null,
    }));
  };

  const prevStep = () => {
    setFormState((prev) => ({
      ...prev,
      step: prev.step - 1,
      error: null,
    }));
  };

  const setLoading = (loading: boolean) => {
    setFormState((prev) => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setFormState((prev) => ({ ...prev, error }));
  };

  const setEmailSent = (email_sent: boolean) => {
    setFormState((prev) => ({ ...prev, email_sent }));
  };

  const handleSignupComplete = async (formData: SignupFormData) => {
    setLoading(true);
    setError(null);

    try {
      // Use Supabase auth directly
      const { data, error: signupError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            full_name: formData.full_name,
            account_type: formData.account_type,
            organization_name: formData.organization_name,
          },
        },
      });

      if (signupError) {
        setError(signupError.message);
        return;
      }

      if (data.user && !data.user.email_confirmed_at) {
        // Move to email verification step
        setEmailSent(true);
        nextStep();
      } else {
        // User is already verified, redirect to dashboard
        router.push(`/${locale}/dashboard?welcome=true`);
      }
    } catch (error) {
      console.error('Signup error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailVerified = async (userId: string) => {
    setLoading(true);
    setError(null);

    try {
      // Get the current session to verify the user is authenticated
      const { data: sessionData } = await supabase.auth.getSession();

      if (sessionData.session?.user) {
        // User is verified and authenticated, proceed to next step
        if (invitationToken) {
          router.push(`/${locale}/dashboard?welcome=true`);
        } else {
          nextStep(); // Move to profile completion
        }
      } else {
        setError('Email verification failed. Please try again.');
      }
    } catch (error) {
      console.error('Email verification error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileComplete = async (profileData: Partial<SignupFormData>) => {
    setLoading(true);
    setError(null);

    try {
      // For now, just redirect to dashboard
      // Profile completion can be implemented later with proper database setup
      router.push(`/${locale}/dashboard?welcome=true`);
    } catch (error) {
      console.error('Profile completion error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Don't render anything if we're loading invitation data
  if (invitationToken && !invitationData && !formState.error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">{t('loading.invitation')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Progress indicator */}
      <div className="flex justify-center">
        <div className="flex space-x-2">
          {[1, 2, 3, 4].map((step) => (
            <div
              key={step}
              className={`h-3 w-3 rounded-full ${
                step <= formState.step ? 'bg-indigo-600' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Step content */}
      {formState.step === 1 && (
        <AccountTypeSelection
          selectedType={formState.data.account_type}
          onSelect={(type) => updateFormData({ account_type: type })}
          onNext={nextStep}
          disabled={!!invitationToken} // Disable if invitation signup
          invitationData={invitationData}
        />
      )}

      {formState.step === 2 && (
        <BasicInformation
          formData={formState.data}
          onUpdate={updateFormData}
          onNext={handleSignupComplete}
          onBack={prevStep}
          loading={formState.loading}
          error={formState.error}
          invitationData={invitationData}
        />
      )}

      {formState.step === 3 && (
        <EmailVerification
          email={formState.data.email || ''}
          onVerified={handleEmailVerified}
          onBack={prevStep}
          loading={formState.loading}
          error={formState.error}
        />
      )}

      {formState.step === 4 && (
        <ProfileCompletion
          formData={formState.data}
          onUpdate={updateFormData}
          onComplete={handleProfileComplete}
          onBack={prevStep}
          loading={formState.loading}
          error={formState.error}
        />
      )}
    </div>
  );
}
